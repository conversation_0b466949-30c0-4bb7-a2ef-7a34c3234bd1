#!/bin/bash

# DassoShu Reader - App Size Analysis Script
# Comprehensive analysis of app size and optimization opportunities

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
ANALYSIS_DIR="build-analysis"
REPORT_FILE="$ANALYSIS_DIR/size_analysis_$(date +%Y%m%d_%H%M%S).md"

echo -e "${BLUE}📊 DassoShu Reader - App Size Analysis${NC}"
echo "=============================================="

# Create analysis directory
mkdir -p "$ANALYSIS_DIR"

# Function to log with timestamp
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${PURPLE}[INFO] $1${NC}"
}

# Function to format file size
format_size() {
    local size=$1
    if [ $size -gt 1048576 ]; then
        echo "$(($size / 1048576)) MB"
    elif [ $size -gt 1024 ]; then
        echo "$(($size / 1024)) KB"
    else
        echo "$size B"
    fi
}

# Function to analyze Android APK
analyze_android() {
    log "Analyzing Android APK..."
    
    local apk_path="build/app/outputs/flutter-apk/app-release.apk"
    local aab_path="build/app/outputs/bundle/release/app-release.aab"
    
    if [ ! -f "$apk_path" ]; then
        warning "Release APK not found. Building..."
        flutter build apk --release --analyze-size --target-platform android-arm64
    fi
    
    if [ -f "$apk_path" ]; then
        local apk_size=$(stat -f%z "$apk_path" 2>/dev/null || stat -c%s "$apk_path" 2>/dev/null)
        info "APK Size: $(format_size $apk_size)"
        
        # Extract APK for detailed analysis
        local extract_dir="$ANALYSIS_DIR/apk_extracted"
        mkdir -p "$extract_dir"
        unzip -q "$apk_path" -d "$extract_dir" 2>/dev/null || true
        
        # Analyze APK contents
        echo "## Android APK Analysis" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        echo "**APK Size:** $(format_size $apk_size)" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        
        # Analyze major components
        if [ -d "$extract_dir" ]; then
            echo "### APK Contents Breakdown" >> "$REPORT_FILE"
            echo "" >> "$REPORT_FILE"
            
            # Flutter assets
            if [ -d "$extract_dir/assets/flutter_assets" ]; then
                local assets_size=$(du -sb "$extract_dir/assets/flutter_assets" | cut -f1)
                echo "- **Flutter Assets:** $(format_size $assets_size)" >> "$REPORT_FILE"
            fi
            
            # Native libraries
            if [ -d "$extract_dir/lib" ]; then
                local lib_size=$(du -sb "$extract_dir/lib" | cut -f1)
                echo "- **Native Libraries:** $(format_size $lib_size)" >> "$REPORT_FILE"
                
                # Analyze architecture-specific libraries
                for arch in "$extract_dir/lib"/*; do
                    if [ -d "$arch" ]; then
                        local arch_name=$(basename "$arch")
                        local arch_size=$(du -sb "$arch" | cut -f1)
                        echo "  - **$arch_name:** $(format_size $arch_size)" >> "$REPORT_FILE"
                    fi
                done
            fi
            
            # Resources
            if [ -f "$extract_dir/resources.arsc" ]; then
                local res_size=$(stat -f%z "$extract_dir/resources.arsc" 2>/dev/null || stat -c%s "$extract_dir/resources.arsc" 2>/dev/null)
                echo "- **Resources:** $(format_size $res_size)" >> "$REPORT_FILE"
            fi
            
            # DEX files
            local dex_total=0
            for dex in "$extract_dir"/*.dex; do
                if [ -f "$dex" ]; then
                    local dex_size=$(stat -f%z "$dex" 2>/dev/null || stat -c%s "$dex" 2>/dev/null)
                    dex_total=$((dex_total + dex_size))
                fi
            done
            if [ $dex_total -gt 0 ]; then
                echo "- **DEX Files:** $(format_size $dex_total)" >> "$REPORT_FILE"
            fi
        fi
        
        echo "" >> "$REPORT_FILE"
    fi
    
    # Analyze AAB if available
    if [ -f "$aab_path" ]; then
        local aab_size=$(stat -f%z "$aab_path" 2>/dev/null || stat -c%s "$aab_path" 2>/dev/null)
        echo "**AAB Size:** $(format_size $aab_size)" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
}

# Function to analyze iOS IPA
analyze_ios() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        warning "iOS analysis skipped - macOS required"
        return
    fi
    
    log "Analyzing iOS IPA..."
    
    local ipa_path=$(find build/ios/Release-iphoneos -name "*.ipa" | head -1)
    
    if [ ! -f "$ipa_path" ]; then
        warning "Release IPA not found. Building..."
        flutter build ios --release --no-codesign
        
        # Create IPA manually
        local app_path="build/ios/Release-iphoneos/Runner.app"
        if [ -d "$app_path" ]; then
            cd build/ios/Release-iphoneos
            mkdir -p Payload
            cp -r Runner.app Payload/
            zip -r "DassoReader-iOS-$(date +%Y%m%d-%H%M%S).ipa" Payload
            rm -rf Payload
            cd - > /dev/null
            ipa_path="build/ios/Release-iphoneos/DassoReader-iOS-*.ipa"
        fi
    fi
    
    if [ -f "$ipa_path" ]; then
        local ipa_size=$(stat -f%z "$ipa_path" 2>/dev/null || stat -c%s "$ipa_path" 2>/dev/null)
        info "IPA Size: $(format_size $ipa_size)"
        
        echo "## iOS IPA Analysis" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        echo "**IPA Size:** $(format_size $ipa_size)" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        
        # Extract IPA for analysis
        local extract_dir="$ANALYSIS_DIR/ipa_extracted"
        mkdir -p "$extract_dir"
        unzip -q "$ipa_path" -d "$extract_dir" 2>/dev/null || true
        
        # Analyze IPA contents
        if [ -d "$extract_dir/Payload/Runner.app" ]; then
            local app_dir="$extract_dir/Payload/Runner.app"
            
            echo "### IPA Contents Breakdown" >> "$REPORT_FILE"
            echo "" >> "$REPORT_FILE"
            
            # Flutter assets
            if [ -d "$app_dir/Frameworks/App.framework/flutter_assets" ]; then
                local assets_size=$(du -sb "$app_dir/Frameworks/App.framework/flutter_assets" | cut -f1)
                echo "- **Flutter Assets:** $(format_size $assets_size)" >> "$REPORT_FILE"
            fi
            
            # Frameworks
            if [ -d "$app_dir/Frameworks" ]; then
                local frameworks_size=$(du -sb "$app_dir/Frameworks" | cut -f1)
                echo "- **Frameworks:** $(format_size $frameworks_size)" >> "$REPORT_FILE"
                
                # Analyze individual frameworks
                for framework in "$app_dir/Frameworks"/*; do
                    if [ -d "$framework" ]; then
                        local fw_name=$(basename "$framework")
                        local fw_size=$(du -sb "$framework" | cut -f1)
                        echo "  - **$fw_name:** $(format_size $fw_size)" >> "$REPORT_FILE"
                    fi
                done
            fi
            
            # Main executable
            if [ -f "$app_dir/Runner" ]; then
                local exec_size=$(stat -f%z "$app_dir/Runner" 2>/dev/null || stat -c%s "$app_dir/Runner" 2>/dev/null)
                echo "- **Main Executable:** $(format_size $exec_size)" >> "$REPORT_FILE"
            fi
        fi
        
        echo "" >> "$REPORT_FILE"
    fi
}

# Function to analyze assets
analyze_assets() {
    log "Analyzing assets..."
    
    echo "## Assets Analysis" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # Analyze fonts
    if [ -d "assets/fonts" ]; then
        echo "### Fonts" >> "$REPORT_FILE"
        local total_font_size=0
        for font in assets/fonts/*; do
            if [ -f "$font" ]; then
                local font_size=$(stat -f%z "$font" 2>/dev/null || stat -c%s "$font" 2>/dev/null)
                total_font_size=$((total_font_size + font_size))
                echo "- **$(basename "$font"):** $(format_size $font_size)" >> "$REPORT_FILE"
            fi
        done
        echo "- **Total Fonts:** $(format_size $total_font_size)" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
    
    # Analyze images
    if [ -d "assets/images" ]; then
        echo "### Images" >> "$REPORT_FILE"
        local total_image_size=0
        for image in assets/images/*; do
            if [ -f "$image" ]; then
                local image_size=$(stat -f%z "$image" 2>/dev/null || stat -c%s "$image" 2>/dev/null)
                total_image_size=$((total_image_size + image_size))
                echo "- **$(basename "$image"):** $(format_size $image_size)" >> "$REPORT_FILE"
            fi
        done
        echo "- **Total Images:** $(format_size $total_image_size)" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
    
    # Analyze audio files
    if [ -d "assets/audio" ]; then
        echo "### Audio Files" >> "$REPORT_FILE"
        local total_audio_size=0
        for audio_dir in assets/audio/*; do
            if [ -d "$audio_dir" ]; then
                local dir_size=$(du -sb "$audio_dir" | cut -f1)
                total_audio_size=$((total_audio_size + dir_size))
                echo "- **$(basename "$audio_dir"):** $(format_size $dir_size)" >> "$REPORT_FILE"
            fi
        done
        echo "- **Total Audio:** $(format_size $total_audio_size)" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
    
    # Analyze dictionaries
    if [ -d "assets/dictionary" ]; then
        echo "### Dictionaries" >> "$REPORT_FILE"
        local total_dict_size=0
        for dict in assets/dictionary/*; do
            if [ -f "$dict" ]; then
                local dict_size=$(stat -f%z "$dict" 2>/dev/null || stat -c%s "$dict" 2>/dev/null)
                total_dict_size=$((total_dict_size + dict_size))
                echo "- **$(basename "$dict"):** $(format_size $dict_size)" >> "$REPORT_FILE"
            fi
        done
        echo "- **Total Dictionaries:** $(format_size $total_dict_size)" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
}

# Function to generate optimization recommendations
generate_recommendations() {
    log "Generating optimization recommendations..."
    
    echo "## Optimization Recommendations" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    echo "### Immediate Actions" >> "$REPORT_FILE"
    echo "1. **Enable R8 full mode** for Android builds" >> "$REPORT_FILE"
    echo "2. **Implement font subsetting** for Chinese characters" >> "$REPORT_FILE"
    echo "3. **Compress audio files** using Opus codec" >> "$REPORT_FILE"
    echo "4. **Optimize images** using WebP format where possible" >> "$REPORT_FILE"
    echo "5. **Remove unused assets** and dependencies" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    echo "### Advanced Optimizations" >> "$REPORT_FILE"
    echo "1. **Implement code splitting** for feature modules" >> "$REPORT_FILE"
    echo "2. **Use dynamic feature delivery** for Android" >> "$REPORT_FILE"
    echo "3. **Implement lazy loading** for non-critical features" >> "$REPORT_FILE"
    echo "4. **Optimize dictionary loading** with compression" >> "$REPORT_FILE"
    echo "5. **Consider on-demand asset delivery**" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    echo "### Monitoring" >> "$REPORT_FILE"
    echo "1. **Set up size monitoring** in CI/CD pipeline" >> "$REPORT_FILE"
    echo "2. **Track size regression** with each release" >> "$REPORT_FILE"
    echo "3. **Monitor user feedback** on app size" >> "$REPORT_FILE"
    echo "4. **Regular size audits** (monthly)" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
}

# Main analysis function
main() {
    # Initialize report
    cat > "$REPORT_FILE" << EOF
# DassoShu Reader - App Size Analysis Report

**Generated:** $(date)
**Analysis Version:** 1.0

## Summary

This report provides a comprehensive analysis of the DassoShu Reader app size and identifies optimization opportunities.

EOF

    # Run analyses
    analyze_assets
    analyze_android
    analyze_ios
    generate_recommendations
    
    # Add footer
    echo "---" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    echo "*Report generated by DassoShu Reader build tools*" >> "$REPORT_FILE"
    
    log "✅ Analysis completed successfully!"
    log "📊 Report saved to: $REPORT_FILE"
    
    # Display summary
    echo ""
    echo -e "${BLUE}📋 Analysis Summary${NC}"
    echo "==================="
    
    if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
        local apk_size=$(stat -f%z "build/app/outputs/flutter-apk/app-release.apk" 2>/dev/null || stat -c%s "build/app/outputs/flutter-apk/app-release.apk" 2>/dev/null)
        echo -e "📱 Android APK: ${GREEN}$(format_size $apk_size)${NC}"
    fi
    
    if [ -f "build/app/outputs/bundle/release/app-release.aab" ]; then
        local aab_size=$(stat -f%z "build/app/outputs/bundle/release/app-release.aab" 2>/dev/null || stat -c%s "build/app/outputs/bundle/release/app-release.aab" 2>/dev/null)
        echo -e "📦 Android AAB: ${GREEN}$(format_size $aab_size)${NC}"
    fi
    
    local ipa_path=$(find build/ios/Release-iphoneos -name "*.ipa" 2>/dev/null | head -1)
    if [ -f "$ipa_path" ]; then
        local ipa_size=$(stat -f%z "$ipa_path" 2>/dev/null || stat -c%s "$ipa_path" 2>/dev/null)
        echo -e "📱 iOS IPA: ${GREEN}$(format_size $ipa_size)${NC}"
    fi
    
    echo ""
    echo -e "${PURPLE}📊 Full report: $REPORT_FILE${NC}"
}

# Run main function
main "$@"
