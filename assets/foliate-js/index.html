<!DOCTYPE html>
<html lang="en">
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
<title>Anx Reader</title>
<style>
  html {
    height: 100vh;
  }

  body {
    -webkit-user-select: none;
    user-select: none;
    margin: 0 !important;
    height: 100vh;
  }


  #footnote-dialog {
    /*
    padding: var(safe-area-inset-top) var(safe-area-inset-right) var(safe-area-inset-bottom) var(safe-area-inset-left);
    */
    position: fixed;
    width: 80vw;
    height: 80vh;
    max-width: 400px;
    max-height: 200px;
    min-width: 300px;
    min-height: 200px;
    border-radius: 15px;
    border: 1px solid grey;
    -webkit-user-select: none;
    user-select: none;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    outline: none;
    z-index: 1000;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
  }

  #footnote-dialog main {
    overflow: auto;
    width: 100%;
    height: 100%;
  }
</style>
<div id="footnote-dialog">
  <main></main>
</div>
<script>
  console.log("AnxUA", navigator.userAgent);

  // Global text selection mode - ensure it's available before bundle loads
  let textSelectionMode = 'free'; // 'free' or 'segmentation'

  // Function to set text selection mode - globally accessible
  window.setTextSelectionMode = (mode) => {
    textSelectionMode = mode;
    window.textSelectionMode = mode; // Update the global reference
    console.log('Text selection mode set to:', mode);
  };

  // Make textSelectionMode accessible to bundle
  window.textSelectionMode = textSelectionMode;
  window.getTextSelectionMode = () => window.textSelectionMode;
</script>
<script src="./dist/bundle.js" type="module"></script>
<script src="./dist/pdf-legacy.js"></script>
<script>
  // Real-time segmentation logic - runs after bundle is loaded
  console.log('🎯 Loading real-time segmentation logic');

  // Wait for the bundle to load and then override selection behavior
  window.addEventListener('load', () => {
    console.log('🎯 Window loaded, setting up segmentation override');

    // Function to adjust selection to word boundaries
    const adjustSelectionToWordBoundaries = async (doc, range, text) => {
      console.log('🎯 adjustSelectionToWordBoundaries called with text:', text);

      try {
        // Call Flutter to get word boundaries
        const result = await window.flutter_inappwebview.callHandler('getWordBoundariesForSelection', {
          text: text,
          startOffset: range.startOffset,
          endOffset: range.endOffset
        });

        console.log('🎯 Word boundaries result:', result);

        if (result && result.adjustedText && result.adjustedText !== text) {
          console.log('🎯 Adjusting selection from "' + text + '" to "' + result.adjustedText + '"');

          // Find the adjusted text in the document
          const walker = doc.createTreeWalker(
            range.commonAncestorContainer,
            NodeFilter.SHOW_TEXT,
            null,
            false
          );

          let node;
          while (node = walker.nextNode()) {
            const nodeText = node.textContent;
            const adjustedIndex = nodeText.indexOf(result.adjustedText);

            if (adjustedIndex !== -1) {
              const newRange = doc.createRange();
              newRange.setStart(node, adjustedIndex);
              newRange.setEnd(node, adjustedIndex + result.adjustedText.length);
              return newRange;
            }
          }
        }

        return range; // Return original range if no adjustment needed
      } catch (e) {
        console.error('❌ Error adjusting selection:', e);
        return range;
      }
    };

    // Override Flutter handler to intercept selection before context menu
    const originalCallHandler = window.flutter_inappwebview?.callHandler;
    if (originalCallHandler) {
      window.flutter_inappwebview.callHandler = async function(handlerName, ...args) {
        console.log('🎯 Flutter handler called:', handlerName, args);

        // Intercept getWordBoundariesForSelection calls
        if (handlerName === 'getWordBoundariesForSelection') {
          const currentMode = window.getTextSelectionMode ? window.getTextSelectionMode() : 'free';
          console.log('🎯 getWordBoundariesForSelection called in mode:', currentMode);

          if (currentMode === 'segmentation') {
            console.log('🚀 INTERCEPTING getWordBoundariesForSelection in segmentation mode');

            // Get current selection
            const selection = document.getSelection();
            if (selection && selection.rangeCount > 0) {
              const text = selection.toString().trim();
              const isChinese = /[\u4e00-\u9fff]/.test(text);

              console.log('🎯 Current selection:', text, 'isChinese:', isChinese);

              if (isChinese) {
                // Call the original handler to get word boundaries
                const result = await originalCallHandler.call(this, handlerName, ...args);
                console.log('🎯 Original handler result:', result);

                // If we got an adjusted text, update the selection immediately
                if (result && result.adjustedText && result.adjustedText !== text) {
                  console.log('🚀 APPLYING REAL-TIME SEGMENTATION: "' + text + '" -> "' + result.adjustedText + '"');

                  try {
                    // Find and select the adjusted text
                    const range = selection.getRangeAt(0);
                    const container = range.commonAncestorContainer;

                    // Search for the adjusted text in the container
                    const walker = document.createTreeWalker(
                      container.nodeType === Node.TEXT_NODE ? container.parentNode : container,
                      NodeFilter.SHOW_TEXT,
                      null,
                      false
                    );

                    let node;
                    while (node = walker.nextNode()) {
                      const nodeText = node.textContent;
                      const adjustedIndex = nodeText.indexOf(result.adjustedText);

                      if (adjustedIndex !== -1) {
                        const newRange = document.createRange();
                        newRange.setStart(node, adjustedIndex);
                        newRange.setEnd(node, adjustedIndex + result.adjustedText.length);

                        selection.removeAllRanges();
                        selection.addRange(newRange);

                        console.log('✅ Selection updated to:', result.adjustedText);
                        break;
                      }
                    }
                  } catch (e) {
                    console.error('❌ Error updating selection:', e);
                  }
                }

                return result;
              }
            }
          }
        }

        // Call original handler for all other cases
        return await originalCallHandler.call(this, handlerName, ...args);
      };

      console.log('🎯 Flutter handler intercepted successfully');
    }

    // Override selection handling for all documents
    const setupSegmentationForDocument = (doc) => {
      console.log('🎯 Setting up segmentation for document');
    };

    // Set up segmentation for the main document
    setupSegmentationForDocument(document);

    // Monitor for new iframes/documents being added (for EPUB content)
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check for iframes
            const iframes = node.tagName === 'IFRAME' ? [node] : node.querySelectorAll('iframe');
            iframes.forEach((iframe) => {
              iframe.addEventListener('load', () => {
                try {
                  const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                  if (iframeDoc) {
                    console.log('🎯 Setting up segmentation for iframe document');
                    setupSegmentationForDocument(iframeDoc);
                  }
                } catch (e) {
                  console.log('🎯 Cannot access iframe document (cross-origin)');
                }
              });
            });
          }
        });
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });

    console.log('🎯 Real-time segmentation setup complete');
  });
</script>