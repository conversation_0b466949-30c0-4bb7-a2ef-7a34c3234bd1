import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Widget optimization utilities to reduce rebuild jank
class WidgetOptimizer {
  static final WidgetOptimizer _instance = WidgetOptimizer._internal();
  factory WidgetOptimizer() => _instance;
  WidgetOptimizer._internal();

  final Map<String, DateTime> _lastRebuildTimes = {};
  final Map<String, int> _rebuildCounts = {};
  Timer? _reportTimer;
  bool _aggressiveOptimizationEnabled = false;

  /// Initialize the widget optimizer
  void initialize() {
    if (kDebugMode) {
      // Report rebuild statistics every 30 seconds
      _reportTimer = Timer.periodic(const Duration(seconds: 30), (_) {
        _reportRebuildStatistics();
      });
    }
  }

  /// Track widget rebuild for optimization analysis
  void trackRebuild(String widgetName) {
    if (!kDebugMode) return;

    final now = DateTime.now();
    final lastRebuild = _lastRebuildTimes[widgetName];

    _rebuildCounts[widgetName] = (_rebuildCounts[widgetName] ?? 0) + 1;
    _lastRebuildTimes[widgetName] = now;

    // Detect rapid rebuilds (potential jank source)
    if (lastRebuild != null) {
      final timeSinceLastRebuild = now.difference(lastRebuild);
      if (timeSinceLastRebuild.inMilliseconds < 16) {
        AnxLog.warning(
          '🔄 Rapid rebuild detected: $widgetName (${timeSinceLastRebuild.inMilliseconds}ms since last)',
        );
      }
    }
  }

  /// Report rebuild statistics
  void _reportRebuildStatistics() {
    if (_rebuildCounts.isEmpty) return;

    final sortedWidgets = _rebuildCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final topRebuilders = sortedWidgets.take(5);

    AnxLog.info('📊 Top rebuilding widgets in last 30s:');
    for (final entry in topRebuilders) {
      AnxLog.info('  ${entry.key}: ${entry.value} rebuilds');
    }

    // Reset counters
    _rebuildCounts.clear();
  }

  /// Enable aggressive optimization for critical performance issues
  void enableAggressiveOptimization() {
    _aggressiveOptimizationEnabled = true;
    // TEMPORARILY DISABLED FOR iOS DEBUGGING - CLEANER LOGS
    // if (kDebugMode) {
    //   AnxLog.info('🚀 Aggressive widget optimization enabled');
    // }
  }

  /// Disable aggressive optimization
  void disableAggressiveOptimization() {
    _aggressiveOptimizationEnabled = false;
    if (kDebugMode) {
      AnxLog.info('🚀 Aggressive widget optimization disabled');
    }
  }

  /// Optimize memory usage by reducing widget caching
  void optimizeMemoryUsage() {
    // Clear rebuild tracking to free memory
    _lastRebuildTimes.clear();
    _rebuildCounts.clear();

    if (kDebugMode) {
      AnxLog.info('🧹 Widget optimizer memory cleaned up');
    }
  }

  /// Reduce rebuilds by implementing stricter rebuild controls
  void reduceRebuilds() {
    if (kDebugMode) {
      AnxLog.info('🔄 Implementing rebuild reduction strategies');
    }

    // This method serves as a signal to widgets using OptimizedState
    // to be more conservative with rebuilds
  }

  /// Check if aggressive optimization is enabled
  bool get isAggressiveOptimizationEnabled => _aggressiveOptimizationEnabled;

  /// Dispose resources
  void dispose() {
    _reportTimer?.cancel();
    _lastRebuildTimes.clear();
    _rebuildCounts.clear();
    _aggressiveOptimizationEnabled = false;
  }
}

/// Optimized StatefulWidget that tracks rebuilds
abstract class OptimizedStatefulWidget extends StatefulWidget {
  const OptimizedStatefulWidget({super.key});

  @override
  OptimizedState<OptimizedStatefulWidget> createState();
}

/// Optimized State that tracks rebuilds and provides optimization utilities
abstract class OptimizedState<T extends OptimizedStatefulWidget>
    extends State<T> {
  late final String _widgetName;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _widgetName = widget.runtimeType.toString();
  }

  @override
  Widget build(BuildContext context) {
    WidgetOptimizer().trackRebuild(_widgetName);
    return buildOptimized(context);
  }

  /// Override this instead of build() for optimized widgets
  Widget buildOptimized(BuildContext context);

  /// Debounced setState to prevent rapid rebuilds
  void setStateDebounced(
    VoidCallback fn, {
    Duration delay = const Duration(milliseconds: 8),
  }) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, () {
      if (mounted) {
        setState(fn);
      }
    });
  }

  /// Safe setState that checks if widget is still mounted
  void setStateSafe(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    }
  }

  /// Post-frame setState to avoid setState during build
  void setStatePostFrame(VoidCallback fn) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      setStateSafe(fn);
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
}

/// Mixin for widgets that need to prevent unnecessary rebuilds
mixin RebuildOptimizationMixin<T extends StatefulWidget> on State<T> {
  Object? _lastData;

  /// Check if rebuild is necessary based on data changes
  bool shouldRebuild(Object? newData) {
    if (_lastData != newData) {
      _lastData = newData;
      return true;
    }
    return false;
  }

  /// Optimized setState that only rebuilds when necessary
  void setStateOptimized(VoidCallback fn, Object? data) {
    if (shouldRebuild(data)) {
      setState(fn);
    }
  }
}

/// Widget that defers heavy operations to prevent jank
class DeferredWidget extends StatefulWidget {
  const DeferredWidget({
    super.key,
    required this.builder,
    this.placeholder,
    this.deferDuration = const Duration(milliseconds: 16),
  });

  final Widget Function(BuildContext context) builder;
  final Widget? placeholder;
  final Duration deferDuration;

  @override
  State<DeferredWidget> createState() => _DeferredWidgetState();
}

class _DeferredWidgetState extends State<DeferredWidget> {
  bool _isReady = false;

  @override
  void initState() {
    super.initState();
    _deferExecution();
  }

  void _deferExecution() {
    Timer(widget.deferDuration, () {
      if (mounted) {
        setState(() {
          _isReady = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isReady) {
      return widget.builder(context);
    }

    return widget.placeholder ?? const SizedBox.shrink();
  }
}

/// Widget that builds children in chunks to prevent jank
class ChunkedListBuilder extends StatefulWidget {
  const ChunkedListBuilder({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.chunkSize = 10,
    this.chunkDelay = const Duration(milliseconds: 16),
    this.placeholder,
  });

  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final int chunkSize;
  final Duration chunkDelay;
  final Widget? placeholder;

  @override
  State<ChunkedListBuilder> createState() => _ChunkedListBuilderState();
}

class _ChunkedListBuilderState extends State<ChunkedListBuilder> {
  int _renderedCount = 0;
  Timer? _chunkTimer;

  @override
  void initState() {
    super.initState();
    _buildNextChunk();
  }

  void _buildNextChunk() {
    if (_renderedCount >= widget.itemCount) return;

    final nextChunkSize =
        (widget.itemCount - _renderedCount).clamp(0, widget.chunkSize);
    _renderedCount += nextChunkSize;

    if (mounted) {
      setState(() {});
    }

    if (_renderedCount < widget.itemCount) {
      _chunkTimer = Timer(widget.chunkDelay, _buildNextChunk);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_renderedCount == 0) {
      return widget.placeholder ?? const CircularProgressIndicator();
    }

    return ListView.builder(
      itemCount: _renderedCount,
      itemBuilder: widget.itemBuilder,
    );
  }

  @override
  void dispose() {
    _chunkTimer?.cancel();
    super.dispose();
  }
}

/// Utility for measuring widget build performance
class BuildPerformanceMeasurer {
  static final Map<String, List<Duration>> _buildTimes = {};

  /// Measure build time for a widget
  static T measureBuild<T>(String widgetName, T Function() builder) {
    if (!kDebugMode) return builder();

    final stopwatch = Stopwatch()..start();
    final result = builder();
    stopwatch.stop();

    final buildTime = stopwatch.elapsed;
    _buildTimes.putIfAbsent(widgetName, () => []).add(buildTime);

    // Keep only recent measurements
    final times = _buildTimes[widgetName]!;
    if (times.length > 100) {
      times.removeRange(0, times.length - 100);
    }

    // Log slow builds
    if (buildTime.inMilliseconds > 16) {
      AnxLog.warning(
        '🐌 Slow widget build: $widgetName took ${buildTime.inMilliseconds}ms',
      );
    }

    return result;
  }

  /// Get average build time for a widget
  static Duration? getAverageBuildTime(String widgetName) {
    final times = _buildTimes[widgetName];
    if (times == null || times.isEmpty) return null;

    final totalMicroseconds =
        times.fold<int>(0, (sum, time) => sum + time.inMicroseconds);
    return Duration(microseconds: totalMicroseconds ~/ times.length);
  }

  /// Get build performance report
  static Map<String, Duration> getPerformanceReport() {
    final report = <String, Duration>{};

    for (final entry in _buildTimes.entries) {
      final avgTime = getAverageBuildTime(entry.key);
      if (avgTime != null) {
        report[entry.key] = avgTime;
      }
    }

    return report;
  }
}
