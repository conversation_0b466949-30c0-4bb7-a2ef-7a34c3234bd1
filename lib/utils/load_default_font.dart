import 'dart:io';

import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

Future<void> loadDefaultFont() async {
  final fontDir = getFontDir();

  // Ensure font directory exists
  if (!fontDir.existsSync()) {
    fontDir.createSync(recursive: true);
  }

  // Load NotoSansSC Regular font
  final notoSansSCRegular =
      await rootBundle.load('assets/fonts/NotoSansSC-Regular.ttf');
  final regularFontFile = File('${fontDir.path}/NotoSansSC-Regular.ttf');
  if (!regularFontFile.existsSync()) {
    regularFontFile.writeAsBytesSync(notoSansSCRegular.buffer.asUint8List());
    AnxLog.info(
      '📁 Font: NotoSansSC-Regular.ttf extracted to ${regularFontFile.path}',
    );
  } else {
    AnxLog.info(
      '📁 Font: NotoSansSC-Regular.ttf already exists at ${regularFontFile.path}',
    );
  }

  // Load NotoSansSC Bold font
  final notoSansSCBold =
      await rootBundle.load('assets/fonts/NotoSansSC-Bold.ttf');
  final boldFontFile = File('${fontDir.path}/NotoSansSC-Bold.ttf');
  if (!boldFontFile.existsSync()) {
    boldFontFile.writeAsBytesSync(notoSansSCBold.buffer.asUint8List());
    AnxLog.info(
      '📁 Font: NotoSansSC-Bold.ttf extracted to ${boldFontFile.path}',
    );
  } else {
    AnxLog.info(
      '📁 Font: NotoSansSC-Bold.ttf already exists at ${boldFontFile.path}',
    );
  }

  // iOS-specific font verification
  if (defaultTargetPlatform == TargetPlatform.iOS) {
    await _verifyIOSFontLoading(regularFontFile, boldFontFile);
  }
}

/// Verify that Chinese fonts are properly loaded on iOS
Future<void> _verifyIOSFontLoading(File regularFont, File boldFont) async {
  try {
    AnxLog.info('🍎 iOS: Verifying Chinese font loading...');

    // Check if font files exist and are readable
    if (!regularFont.existsSync()) {
      throw Exception(
        'NotoSansSC-Regular.ttf not found at ${regularFont.path}',
      );
    }

    if (!boldFont.existsSync()) {
      throw Exception(
        'NotoSansSC-Bold.ttf not found at ${boldFont.path}',
      );
    }

    // Check file sizes to ensure they're not corrupted
    final regularSize = regularFont.lengthSync();
    final boldSize = boldFont.lengthSync();

    AnxLog.info('🍎 iOS: NotoSansSC-Regular.ttf size: $regularSize bytes');
    AnxLog.info('🍎 iOS: NotoSansSC-Bold.ttf size: $boldSize bytes');

    // Reasonable size check (NotoSansSC fonts should be several MB)
    if (regularSize < 1000000) {
      // Less than 1MB is suspicious
      AnxLog.warning(
        '🍎 iOS: NotoSansSC-Regular.ttf seems too small ($regularSize bytes)',
      );
    }

    if (boldSize < 1000000) {
      // Less than 1MB is suspicious
      AnxLog.warning(
        '🍎 iOS: NotoSansSC-Bold.ttf seems too small ($boldSize bytes)',
      );
    }

    // Test reading font file headers
    final regularBytes = regularFont.readAsBytesSync();
    final boldBytes = boldFont.readAsBytesSync();

    // Check TTF magic number (0x00010000 for TTF files)
    if (regularBytes.length >= 4) {
      final magic = (regularBytes[0] << 24) |
          (regularBytes[1] << 16) |
          (regularBytes[2] << 8) |
          regularBytes[3];
      if (magic == 0x00010000) {
        AnxLog.info('🍎 iOS: NotoSansSC-Regular.ttf has valid TTF header');
      } else {
        AnxLog.warning(
          '🍎 iOS: NotoSansSC-Regular.ttf has invalid TTF header: 0x${magic.toRadixString(16)}',
        );
      }
    }

    if (boldBytes.length >= 4) {
      final magic = (boldBytes[0] << 24) |
          (boldBytes[1] << 16) |
          (boldBytes[2] << 8) |
          boldBytes[3];
      if (magic == 0x00010000) {
        AnxLog.info('🍎 iOS: NotoSansSC-Bold.ttf has valid TTF header');
      } else {
        AnxLog.warning(
          '🍎 iOS: NotoSansSC-Bold.ttf has invalid TTF header: 0x${magic.toRadixString(16)}',
        );
      }
    }

    AnxLog.info('🍎 iOS: Chinese font verification completed successfully');
  } catch (e) {
    AnxLog.severe('🍎 iOS: Font verification failed: $e');
    rethrow;
  }
}
