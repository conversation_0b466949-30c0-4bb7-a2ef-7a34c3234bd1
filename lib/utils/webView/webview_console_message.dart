import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

void webviewConsoleMessage(
  InAppWebViewController controller,
  ConsoleMessage consoleMessage,
) {
  // Filter out unwanted browser-internal messages
  final message = consoleMessage.message;

  // Skip iframe sandbox warnings
  if (message.contains(
    'An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing',
  )) {
    return;
  }

  // Skip ResizeObserver warnings (browser internal)
  if (message.contains(
      'ResizeObserver loop completed with undelivered notifications')) {
    return;
  }

  // Skip other common browser-internal messages
  if (message.contains('Filtered to 0 valid annotations') ||
      message.contains('Non-Error promise rejection captured') ||
      message.contains('Violation') ||
      message.contains('[Violation]')) {
    return;
  }

  // Log remaining messages
  if (consoleMessage.messageLevel == ConsoleMessageLevel.LOG) {
    AnxLog.info('Webview: ${consoleMessage.message}');
  } else if (consoleMessage.messageLevel == ConsoleMessageLevel.WARNING) {
    AnxLog.warning('Webview: ${consoleMessage.message}');
  } else if (consoleMessage.messageLevel == ConsoleMessageLevel.ERROR) {
    AnxLog.severe('Webview: ${consoleMessage.message}');
  }
}
