# DassoShu Reader - Flutter Build Configuration
# Professional build optimization settings for production deployment

# App size optimization settings
size_optimization:
  # Enable tree shaking for unused code elimination
  tree_shake_icons: true
  
  # Font subsetting for Chinese characters
  font_subsetting:
    enabled: true
    # Keep only HSK characters and common punctuation
    character_sets:
      - "hsk_1_6"  # HSK levels 1-6 characters
      - "punctuation"  # Common punctuation marks
      - "numbers"  # 0-9 digits
      - "latin_basic"  # Basic Latin alphabet
  
  # Asset optimization
  assets:
    # Compress images
    image_compression: true
    # Remove unused assets
    remove_unused: true
    # Optimize vector graphics
    svg_optimization: true

# Performance optimization settings
performance:
  # Dart compilation optimizations
  dart_compilation:
    # Enable aggressive optimizations
    optimization_level: "O3"
    # Remove debug information in release
    strip_debug_info: true
    # Enable inlining
    enable_inlining: true
  
  # Memory management
  memory:
    # Garbage collection tuning
    gc_policy: "adaptive"
    # Heap size limits
    max_heap_size: "256MB"
    # Enable memory compaction
    enable_compaction: true
  
  # Startup optimization
  startup:
    # Precompile critical paths
    precompile_critical: true
    # Lazy load non-essential features
    lazy_loading: true
    # Optimize asset loading order
    asset_loading_priority: true

# Platform-specific optimizations
android:
  # APK optimization
  apk:
    # Enable R8 full mode
    r8_full_mode: true
    # Aggressive shrinking
    aggressive_shrinking: true
    # Remove unused resources
    resource_shrinking: true
  
  # Native code optimization
  native:
    # Target specific architectures
    target_architectures:
      - "arm64-v8a"  # Primary target for modern devices
    # Enable native optimizations
    native_optimizations: true
    # Strip native debug symbols
    strip_native_debug: true
  
  # Manifest optimization
  manifest:
    # Remove unused permissions
    remove_unused_permissions: true
    # Optimize for specific Android versions
    target_sdk_optimization: true

ios:
  # IPA optimization
  ipa:
    # Enable bitcode (if supported)
    bitcode: false  # Disabled for Flutter compatibility
    # Optimize for size
    optimize_for_size: true
    # Strip symbols
    strip_symbols: true
  
  # Swift optimization
  swift:
    # Optimization level
    optimization_level: "O"
    # Whole module optimization
    whole_module_optimization: true
    # Enable swift optimizations
    enable_optimizations: true
  
  # Asset catalog optimization
  assets:
    # Optimize app icons
    optimize_app_icons: true
    # Compress images
    image_compression: true
    # Remove unused assets
    remove_unused: true

# Build variants
build_variants:
  # Development build
  debug:
    optimization_level: "O0"
    debug_info: true
    assertions: true
    tree_shaking: false
  
  # Testing build
  profile:
    optimization_level: "O2"
    debug_info: true
    assertions: false
    tree_shaking: true
  
  # Production build
  release:
    optimization_level: "O3"
    debug_info: false
    assertions: false
    tree_shaking: true
    obfuscation: true
    minification: true

# Size targets and monitoring
size_targets:
  # Target sizes (in MB)
  android_apk: 45  # Target APK size
  android_aab: 35  # Target AAB size
  ios_ipa: 50      # Target IPA size
  
  # Asset size limits
  max_asset_size: 5    # Max individual asset size (MB)
  max_font_size: 2     # Max font file size (MB)
  max_image_size: 1    # Max image file size (MB)
  
  # Performance targets
  cold_start_time: 2000   # Target cold start time (ms)
  warm_start_time: 1000   # Target warm start time (ms)
  memory_usage: 150       # Target memory usage (MB)

# Monitoring and reporting
monitoring:
  # Enable build size reporting
  size_reporting: true
  
  # Performance monitoring
  performance_monitoring:
    enabled: true
    # Monitor startup time
    startup_time: true
    # Monitor memory usage
    memory_usage: true
    # Monitor frame rate
    frame_rate: true
  
  # Build analytics
  build_analytics:
    enabled: true
    # Track build times
    build_time_tracking: true
    # Size analysis
    size_analysis: true
    # Dependency analysis
    dependency_analysis: true

# Quality gates
quality_gates:
  # Size limits (build will fail if exceeded)
  max_apk_size: 60      # Maximum APK size (MB)
  max_ipa_size: 70      # Maximum IPA size (MB)
  
  # Performance limits
  max_cold_start: 3000  # Maximum cold start time (ms)
  max_memory_usage: 200 # Maximum memory usage (MB)
  
  # Code quality
  max_method_count: 65536  # Android DEX limit
  min_test_coverage: 80    # Minimum test coverage (%)

# Optimization strategies
strategies:
  # Progressive optimization
  progressive:
    # Start with basic optimizations
    level_1: ["tree_shaking", "asset_compression"]
    # Add advanced optimizations
    level_2: ["code_splitting", "lazy_loading"]
    # Apply aggressive optimizations
    level_3: ["obfuscation", "minification", "dead_code_elimination"]
  
  # Platform-specific strategies
  platform_specific:
    android: ["r8_optimization", "resource_shrinking", "native_stripping"]
    ios: ["swift_optimization", "symbol_stripping", "asset_optimization"]
  
  # Feature-based optimization
  feature_based:
    # Chinese language features
    chinese_support: ["font_subsetting", "character_optimization"]
    # Reading features
    reading_features: ["epub_optimization", "webview_optimization"]
    # AI features
    ai_features: ["model_compression", "api_optimization"]
