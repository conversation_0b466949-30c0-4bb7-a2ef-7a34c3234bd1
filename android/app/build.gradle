plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "io.github.dassodev.dasso_reader"
    compileSdk 35
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "io.github.dassodev.dasso_reader"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release

            // Enable code shrinking, obfuscation, and optimization
            minifyEnabled true
            shrinkResources true

            // ProGuard configuration files (R8 is used by default)
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // Optimize for size and performance
            zipAlignEnabled true
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false

            // Build configuration optimizations
            multiDexEnabled false

            // Disable PNG crunching for faster builds (optional)
            // crunchPngs false
        }

        debug {
            // Debug optimizations
            minifyEnabled false
            shrinkResources false
            debuggable true
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }

        profile {
            // Profile build optimizations
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            zipAlignEnabled true
            debuggable false

            // Keep debug symbols for profiling
            ndk {
                debugSymbolLevel 'SYMBOL_TABLE'
            }
        }
    }
}

flutter {
    source '../..'
}

dependencies {} 