# DassoShu Reader - ProGuard Rules for Production Optimization
# Professional Flutter app with Chinese language learning features

# ===== FLUTTER FRAMEWORK =====
# Keep Flutter framework classes
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keepattributes *Annotation*

# Keep Flutter engine
-keep class io.flutter.embedding.** { *; }
-dontwarn io.flutter.embedding.**

# ===== DART/FLUTTER SPECIFIC =====
# Keep Dart VM service classes
-keep class org.dartlang.** { *; }
-dontwarn org.dartlang.**

# Keep Flutter plugin registrant
-keep class io.flutter.plugins.GeneratedPluginRegistrant { *; }

# ===== CHINESE LANGUAGE SUPPORT =====
# Keep Chinese font library classes
-keep class com.google.android.material.** { *; }
-keep class androidx.core.content.res.** { *; }

# Keep text rendering classes for Chinese characters
-keep class android.text.** { *; }
-keep class android.graphics.** { *; }
-keep class android.graphics.fonts.** { *; }

# ===== WEBVIEW & EPUB RENDERING =====
# Keep WebView classes for EPUB rendering
-keep class android.webkit.** { *; }
-keep class com.pichillilorenzo.flutter_inappwebview.** { *; }
-dontwarn android.webkit.**

# Keep JavaScript interface classes
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# ===== AI SERVICES =====
# Keep HTTP client classes for AI API calls
-keep class okhttp3.** { *; }
-keep class retrofit2.** { *; }
-dontwarn okhttp3.**
-dontwarn retrofit2.**

# Keep JSON serialization classes
-keep class com.google.gson.** { *; }
-keepattributes Signature
-keepattributes *Annotation*
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# ===== DATABASE & STORAGE =====
# Keep SQLite classes
-keep class org.sqlite.** { *; }
-keep class android.database.** { *; }

# Keep file I/O classes
-keep class java.io.** { *; }
-keep class java.nio.** { *; }

# ===== AUDIO & TTS =====
# Keep audio classes for TTS and pronunciation
-keep class android.media.** { *; }
-keep class android.speech.tts.** { *; }

# ===== PLATFORM CHANNELS =====
# Keep method channel classes
-keep class io.github.dassodev.dasso_reader.MainActivity { *; }
-keepclassmembers class io.github.dassodev.dasso_reader.MainActivity {
    public <methods>;
}

# Keep native method implementations
-keepclasseswithmembernames class * {
    native <methods>;
}

# ===== PERFORMANCE MONITORING =====
# Keep performance monitoring classes
-keep class android.os.Debug { *; }
-keep class android.app.ActivityManager { *; }
-keep class android.os.BatteryManager { *; }

# ===== GENERAL ANDROID =====
# Keep Android framework classes
-keep class android.support.** { *; }
-keep class androidx.** { *; }
-dontwarn android.support.**
-dontwarn androidx.**

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep Serializable classes
-keepnames class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# ===== OPTIMIZATION SETTINGS =====
# Enable aggressive optimization
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# ===== KEEP ESSENTIAL CLASSES =====
# Keep application class
-keep public class * extends android.app.Application
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# Keep custom exceptions
-keep public class * extends java.lang.Exception

# ===== REFLECTION SAFETY =====
# Keep classes that use reflection
-keepattributes InnerClasses
-keepattributes EnclosingMethod
-keepattributes Signature
-keepattributes RuntimeVisibleAnnotations
-keepattributes RuntimeInvisibleAnnotations

# ===== FINAL CLEANUP =====
# Remove unused resources
-dontwarn **
-ignorewarnings

# Print mapping for debugging
-printmapping mapping.txt
-printseeds seeds.txt
-printusage usage.txt
